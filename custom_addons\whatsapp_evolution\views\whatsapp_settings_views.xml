<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WhatsApp Settings Form View -->
        <record id="view_whatsapp_settings_form" model="ir.ui.view">
            <field name="name">whatsapp.settings.form</field>
            <field name="model">whatsapp.settings</field>
            <field name="arch" type="xml">
                <form string="WhatsApp Default Settings">
                    <header>
                        <button name="action_save_settings" type="object" string="Save Settings" class="btn-primary"/>
                        <button name="action_test_sms" type="object" string="Test SMS" class="btn-secondary" invisible="not sms_enabled"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name" placeholder="Configuration Name"/></h1>
                        </div>

                        <group>
                            <group name="basic_info">
                                <field name="active"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>

                        <group string="Default Configuration Values">
                            <group name="server_config">
                                <field name="default_server_url" placeholder="https://your-evolution-server.com"/>
                                <field name="default_global_api_key" password="True" placeholder="Enter your Evolution API global key"/>
                            </group>
                        </group>

                        <group string="SMS Gateway Configuration">
                            <group name="sms_config">
                                <field name="sms_enabled"/>
                                <field name="sms_gateway_url" placeholder="http://192.168.1.100:8080/message" invisible="not sms_enabled"/>
                                <field name="sms_username" placeholder="SMS Gateway Username" invisible="not sms_enabled"/>
                                <field name="sms_password" password="True" placeholder="SMS Gateway Password" invisible="not sms_enabled"/>
                            </group>
                        </group>

                        <div class="alert alert-info" role="alert">
                            <h5>
                                <i class="fa fa-info-circle"/>
                                How Default Settings Work
                            </h5>
                            <p>
                                These values will be automatically used when creating new WhatsApp Configurations.
                                Users can still modify these values for individual configurations if needed.
                            </p>
                            <ul>
                                <li><strong>Default Server URL:</strong> The Evolution API server URL that will be pre-filled</li>
                                <li><strong>Default Global API Key:</strong> The global API key that will be pre-filled</li>
                                <li><strong>SMS Gateway:</strong> Configure Android SMS Gateway for sending SMS alongside WhatsApp messages</li>
                            </ul>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp Settings List View -->
        <record id="view_whatsapp_settings_tree" model="ir.ui.view">
            <field name="name">whatsapp.settings.list</field>
            <field name="model">whatsapp.settings</field>
            <field name="arch" type="xml">
                <list string="WhatsApp Default Settings">
                    <field name="name"/>
                    <field name="default_server_url"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>

        <!-- WhatsApp Settings Action -->
        <record id="action_whatsapp_settings" model="ir.actions.act_window">
            <field name="name">WhatsApp Default Settings</field>
            <field name="res_model">whatsapp.settings</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your WhatsApp default settings!
                </p>
                <p>
                    Configure default values for Evolution API Server URL and Global API Key that will be used when creating new WhatsApp configurations.
                </p>
            </field>
        </record>

    </data>
</odoo>
