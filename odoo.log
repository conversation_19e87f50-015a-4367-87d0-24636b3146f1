2025-09-24 10:45:12,664 77432 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 3.47s, 570 queries (+570 extra) 
2025-09-24 10:45:13,425 77432 INFO kayan_whatsapp odoo.modules.registry: verifying fields for every extended model 
2025-09-24 10:45:13,560 77432 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18_cubes\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 10:45:17,971 113224 INFO ? odoo: Odoo version 18.0-20241204 
2025-09-24 10:45:17,971 113224 INFO ? odoo: Using configuration file at C:\odoo18_cubes\server\odoo.conf 
2025-09-24 10:45:17,971 113224 INFO ? odoo: addons paths: ['C:\\odoo18_cubes\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18_cubes\\server\\custom_addons', 'c:\\odoo18_cubes\\server\\odoo\\addons'] 
2025-09-24 10:45:17,971 113224 INFO ? odoo: database: openpg@localhost:5432 
2025-09-24 10:45:18,198 113224 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-09-24 10:45:18,221 113224 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-09-24 10:45:19,865 113224 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8087 
2025-09-24 10:45:20,026 113224 INFO kayan_whatsapp odoo.modules.loading: loading 1 modules... 
2025-09-24 10:45:20,026 113224 INFO kayan_whatsapp odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-24 10:45:20,051 113224 INFO kayan_whatsapp odoo.modules.loading: updating modules list 
2025-09-24 10:45:20,062 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-09-24 10:45:20,326 113224 WARNING kayan_whatsapp odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-09-24 10:45:21,338 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 10:45:21,338 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['WhatsApp Evolution API Integration'] to user __system__ #1 via n/a 
2025-09-24 10:45:22,277 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-09-24 10:45:22,360 113224 INFO kayan_whatsapp odoo.modules.loading: loading 98 modules... 
2025-09-24 10:45:24,017 113224 WARNING kayan_whatsapp odoo.tools.translate: no translation language detected, skipping translation <frame at 0x000002867330CE10, file 'c:\\odoo18_cubes\\server\\custom_addons\\om_account_followup\\wizard\\followup_print.py', line 34, code FollowupPrint> 
Stack (most recent call last):
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18_cubes\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\__init__.py", line 1, in <module>
    from . import wizard
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\__init__.py", line 1, in <module>
    from . import followup_print
  File "<frozen importlib._bootstrap>", line 1415, in _handle_fromlist
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 7, in <module>
    class FollowupPrint(models.TransientModel):
  File "c:\odoo18_cubes\server\custom_addons\om_account_followup\wizard\followup_print.py", line 34, in FollowupPrint
    default=_('Invoices Reminder'))
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 609, in get_text_alias
    module, lang = _get_translation_source(1)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 598, in _get_translation_source
    lang = lang or _get_lang(frame, default_lang)
  File "C:\odoo18_cubes\server\odoo\tools\translate.py", line 589, in _get_lang
    _logger.log(log_level, 'no translation language detected, skipping translation %s', frame, stack_info=True)
2025-09-24 10:45:24,214 113224 INFO kayan_whatsapp odoo.modules.loading: Loading module whatsapp_evolution (79/98) 
2025-09-24 10:45:25,372 113224 INFO kayan_whatsapp odoo.modules.registry: module whatsapp_evolution: creating or updating database tables 
2025-09-24 10:45:26,030 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/whatsapp_evolution_security.xml 
2025-09-24 10:45:26,069 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/security/ir.model.access.csv 
2025-09-24 10:45:26,833 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_settings_data.xml 
2025-09-24 10:45:26,842 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_data.xml 
2025-09-24 10:45:26,861 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_config_default_data.xml 
2025-09-24 10:45:26,865 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_config_views.xml 
2025-09-24 10:45:26,974 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_settings_views.xml 
2025-09-24 10:45:27,015 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_normal_user_views.xml 
2025-09-24 10:45:27,073 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.actions.act_window.view records with IDs: [188, 189] 
2025-09-24 10:45:27,093 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_message_views.xml 
2025-09-24 10:45:27,138 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_send_message_wizard_views.xml 
2025-09-24 10:45:27,162 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_instance_wizard_views.xml 
2025-09-24 10:45:27,190 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,190 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,190 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 5,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have progressbar role
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuenow attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemin attribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,191 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: o_progressbar class must have aria-valuemaxattribute
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 6,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,192 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-whatsapp text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 26,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,194 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: An alert (class alert-*) must have an alert, alertdialog or status role or an alert-link class. Please use alert and alertdialog only for what expects to stop any activity to be read immediately.
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 74,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,196 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-check-circle text-success) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 116,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,197 113224 WARNING kayan_whatsapp odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-exclamation-triangle text-danger) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo18_cubes\\server\\custom_addons\\whatsapp_evolution\\views\\whatsapp_instance_wizard_views.xml',
 'line': 137,
 'name': 'whatsapp.instance.wizard.form',
 'view': ir.ui.view(1967,),
 'view.model': 'whatsapp.instance.wizard',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_whatsapp_instance_wizard_form'} 
2025-09-24 10:45:27,205 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/whatsapp_business_template_views.xml 
2025-09-24 10:45:27,268 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/wizard/whatsapp_sms_test_wizard_views.xml 
2025-09-24 10:45:27,285 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/views/menu_views.xml 
2025-09-24 10:45:27,440 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_business_template_data.xml 
2025-09-24 10:45:27,457 113224 INFO kayan_whatsapp odoo.modules.loading: loading whatsapp_evolution/data/whatsapp_cron_data.xml 
2025-09-24 10:45:27,473 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_module: module whatsapp_evolution: loading translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 10:45:27,473 113224 INFO kayan_whatsapp odoo.tools.translate: loading base translation file c:\odoo18_cubes\server\custom_addons\whatsapp_evolution\i18n\ar.po for language ar_001 
2025-09-24 10:45:27,621 113224 INFO kayan_whatsapp odoo.modules.loading: Module whatsapp_evolution loaded in 3.41s, 554 queries (+554 other) 
2025-09-24 10:45:28,074 113224 INFO kayan_whatsapp odoo.modules.loading: 98 modules loaded in 5.71s, 554 queries (+554 extra) 
2025-09-24 10:45:30,036 113224 INFO kayan_whatsapp odoo.modules.registry: verifying fields for every extended model 
2025-09-24 10:45:30,448 113224 WARNING kayan_whatsapp py.warnings: C:\odoo18_cubes\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18_cubes\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18_cubes\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18_cubes\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18_cubes\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18_cubes\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18_cubes\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "C:\odoo18_cubes\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18_cubes\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18_cubes\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18_cubes\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18_cubes\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-09-24 10:45:32,546 113224 INFO kayan_whatsapp odoo.modules.loading: Modules loaded. 
2025-09-24 10:45:32,562 113224 INFO kayan_whatsapp odoo.modules.registry: Registry changed, signaling through the database 
2025-09-24 10:45:32,571 113224 INFO kayan_whatsapp odoo.modules.registry: Registry loaded in 12.705s 
2025-09-24 10:45:32,579 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-09-24 10:45:35,653 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:35] "GET /odoo/action-691/3 HTTP/1.1" 200 - 99 0.160 15.538
2025-09-24 10:45:36,190 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 17 0.024 0.038
2025-09-24 10:45:36,230 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /web/action/load HTTP/1.1" 200 - 10 0.031 0.066
2025-09-24 10:45:36,459 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.001 0.013
2025-09-24 10:45:36,541 113224 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-24 10:45:36,698 113224 WARNING kayan_whatsapp odoo.http: <function odoo.addons.bus.controllers.main.get_autovacuum_info> called ignoring args {'silent'} 
2025-09-24 10:45:36,708 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /bus/get_autovacuum_info HTTP/1.1" 200 - 4 0.014 0.143
2025-09-24 10:45:36,784 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /web/dataset/call_kw/whatsapp.business.template/get_views HTTP/1.1" 200 - 34 0.092 0.185
2025-09-24 10:45:36,805 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.015 0.017
2025-09-24 10:45:36,820 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:36] "POST /mail/data HTTP/1.1" 200 - 69 0.294 0.080
2025-09-24 10:45:37,159 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:37] "POST /web/dataset/call_kw/whatsapp.business.template/web_read HTTP/1.1" 200 - 5 0.011 0.024
2025-09-24 10:45:38,791 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:38] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.006 0.002
2025-09-24 10:45:39,122 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "GET /odoo/action-691/3 HTTP/1.1" 200 - 15 0.042 0.040
2025-09-24 10:45:39,429 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.014 0.030
2025-09-24 10:45:39,729 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "POST /web/action/load HTTP/1.1" 200 - 9 0.016 0.019
2025-09-24 10:45:39,793 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "POST /web/dataset/call_kw/whatsapp.business.template/get_views HTTP/1.1" 200 - 2 0.000 0.038
2025-09-24 10:45:39,816 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:39] "POST /mail/data HTTP/1.1" 200 - 38 0.053 0.057
2025-09-24 10:45:40,064 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:40] "POST /web/dataset/call_kw/whatsapp.business.template/web_read HTTP/1.1" 200 - 4 0.002 0.014
2025-09-24 10:45:40,136 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:40] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.008 0.006
2025-09-24 10:45:41,852 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:41] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.011
2025-09-24 10:45:42,118 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:42] "POST /web/dataset/call_kw/whatsapp.business.template/web_search_read HTTP/1.1" 200 - 3 0.016 0.002
2025-09-24 10:45:42,355 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:42] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.014
2025-09-24 10:45:45,609 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:45] "POST /web/action/load HTTP/1.1" 200 - 9 0.017 0.016
2025-09-24 10:45:45,964 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:45] "POST /web/dataset/call_kw/whatsapp.settings/get_views HTTP/1.1" 200 - 13 0.001 0.033
2025-09-24 10:45:46,198 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:46] "POST /web/dataset/call_kw/whatsapp.settings/web_search_read HTTP/1.1" 200 - 4 0.008 0.008
2025-09-24 10:45:47,496 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:47] "POST /web/dataset/call_kw/whatsapp.settings/web_search_read HTTP/1.1" 200 - 3 0.000 0.017
2025-09-24 10:45:48,993 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:48] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.010 0.016
2025-09-24 10:45:52,844 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:52] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.009 0.008
2025-09-24 10:45:53,195 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:53] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/get_views HTTP/1.1" 200 - 10 0.002 0.025
2025-09-24 10:45:53,440 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:45:53] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 2 0.000 0.016
2025-09-24 10:46:04,088 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:04] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 5 0.008 0.017
2025-09-24 10:46:26,001 113224 WARNING kayan_whatsapp odoo.http: SMS Gateway connection error: HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: /message (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000286737CE4B0>, 'Connection to ************** timed out. (connect timeout=30)')) 
2025-09-24 10:46:26,001 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:26] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.000 21.596
2025-09-24 10:46:32,714 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 10:46:32,728 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.014s 
2025-09-24 10:46:32,738 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 10:46:32,745 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 10:46:32,762 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 10:46:32,769 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.006s 
2025-09-24 10:46:32,777 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 10:46:32,777 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 10:46:37,624 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:37] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.007 0.011
2025-09-24 10:46:42,316 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:42] "POST /web/dataset/call_kw/whatsapp.settings/web_save HTTP/1.1" 200 - 6 0.007 0.035
2025-09-24 10:46:43,526 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:43] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.003 0.004
2025-09-24 10:46:43,853 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:43] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 1 0.000 0.008
2025-09-24 10:46:52,484 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:46:52] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 3 0.000 0.028
2025-09-24 10:47:13,894 113224 WARNING kayan_whatsapp odoo.http: SMS Gateway connection error: HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000002867378E6C0>, 'Connection to ************** timed out. (connect timeout=30)')) 
2025-09-24 10:47:13,894 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:47:13] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.007 21.079
2025-09-24 10:49:55,687 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:49:55] "POST /web/dataset/call_kw/whatsapp.settings/web_read HTTP/1.1" 200 - 3 0.001 0.017
2025-09-24 10:50:00,286 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:00] "POST /web/dataset/call_kw/whatsapp.settings/web_save HTTP/1.1" 200 - 5 0.006 0.026
2025-09-24 10:50:13,626 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:13] "POST /web/dataset/call_kw/whatsapp.settings/web_save HTTP/1.1" 200 - 5 0.000 0.025
2025-09-24 10:50:15,032 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:15] "POST /web/dataset/call_button/whatsapp.settings/action_test_sms HTTP/1.1" 200 - 2 0.000 0.007
2025-09-24 10:50:15,353 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:15] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/onchange HTTP/1.1" 200 - 1 0.008 0.000
2025-09-24 10:50:22,239 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:22] "POST /web/dataset/call_kw/whatsapp.sms.test.wizard/web_save HTTP/1.1" 200 - 3 0.002 0.007
2025-09-24 10:50:24,398 113224 WARNING kayan_whatsapp odoo.http: Unexpected error: SMS sending failed. Status: 202, Response: {"id":"-HGFO7wIzc-kP1ZcydViS","isEncrypted":false,"recipients":[{"phoneNumber":"+218915688883","state":"Pending"}],"state":"Pending","states":{"Pending":"2025-09-24T13:50:24.308+03:00"}} 
2025-09-24 10:50:24,398 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 10:50:24] "POST /web/dataset/call_button/whatsapp.sms.test.wizard/action_send_test_sms HTTP/1.1" 200 - 3 0.002 1.838
2025-09-24 10:51:32,830 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 10:51:32,836 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.006s 
2025-09-24 10:51:32,840 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 10:51:32,844 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 10:54:32,890 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 10:54:32,898 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.008s 
2025-09-24 10:54:32,906 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 10:54:32,906 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 10:56:32,954 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 10:56:32,963 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.009s 
2025-09-24 10:56:32,969 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 10:56:32,985 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 11:01:33,030 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 11:01:33,035 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.005s 
2025-09-24 11:01:33,041 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 11:01:33,047 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 11:04:33,084 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 11:04:33,091 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.007s 
2025-09-24 11:04:33,095 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 11:04:33,099 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 11:06:33,134 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 11:06:33,142 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.008s 
2025-09-24 11:06:33,147 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 11:06:33,155 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 11:11:33,236 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 11:11:33,408 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.172s 
2025-09-24 11:11:33,416 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 11:11:33,426 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:07:15,757 113224 INFO kayan_whatsapp odoo.addons.base.models.res_device: User 2 inserts device log (OvaDjCeDiQ01hvjXuCefi2FhSRa9Md5SDz5j6MW0XY) 
2025-09-24 12:07:15,763 113224 INFO kayan_whatsapp werkzeug: 127.0.0.1 - - [24/Sep/2025 12:07:15] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 2 0.026 0.078
2025-09-24 12:07:19,871 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-09-24 12:07:19,978 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-09-24 12:07:20,015 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted ir.cron.progress records with IDs: [1, 2] 
2025-09-24 12:07:20,349 113224 INFO kayan_whatsapp odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-09-24 12:07:20,380 113224 INFO kayan_whatsapp odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-09-24 12:07:20,399 113224 INFO kayan_whatsapp odoo.addons.base.models.res_device: GC device logs delete 3 entries 
2025-09-24 12:07:20,403 113224 INFO kayan_whatsapp odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-09-24 12:07:20,472 113224 INFO kayan_whatsapp odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-09-24 12:07:20,513 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted bus.bus records with IDs: [163, 164, 165, 166, 167, 168, 169, 170, 171] 
2025-09-24 12:07:20,955 113224 INFO kayan_whatsapp odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-09-24 12:07:22,040 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted whatsapp.template.test.wizard records with IDs: [3] 
2025-09-24 12:07:22,055 113224 INFO kayan_whatsapp odoo.models.unlink: User #1 deleted whatsapp.sms.test.wizard records with IDs: [1, 2, 3] 
2025-09-24 12:07:22,132 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 2.261s 
2025-09-24 12:07:22,141 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,152 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-09-24 12:07:22,174 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-09-24 12:07:22,186 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.012s 
2025-09-24 12:07:22,197 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,204 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-09-24 12:07:22,219 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-09-24 12:07:22,231 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.012s 
2025-09-24 12:07:22,235 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,246 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-09-24 12:07:22,262 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-09-24 12:07:22,280 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.017s 
2025-09-24 12:07:22,285 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,291 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-09-24 12:07:22,308 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) starting 
2025-09-24 12:07:22,321 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) done in 0.013s 
2025-09-24 12:07:22,330 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,335 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Payment: Post-process transactions' (17) completed 
2025-09-24 12:07:22,349 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) starting 
2025-09-24 12:07:22,356 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) done in 0.007s 
2025-09-24 12:07:22,363 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,373 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Generate Recurring Payments' (49) completed 
2025-09-24 12:07:22,388 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) starting 
2025-09-24 12:07:22,477 113224 INFO kayan_whatsapp odoo.addons.whatsapp_evolution.models.whatsapp_stock_monitor: No low stock products found 
2025-09-24 12:07:22,478 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) done in 0.090s 
2025-09-24 12:07:22,485 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,491 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Check Low Stock Alerts' (52) completed 
2025-09-24 12:07:22,509 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) starting 
2025-09-24 12:07:22,519 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) done in 0.010s 
2025-09-24 12:07:22,523 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,534 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'WhatsApp: Monitor Instance Connections' (54) completed 
2025-09-24 12:07:22,548 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-09-24 12:07:22,574 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.025s 
2025-09-24 12:07:22,581 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,587 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-09-24 12:07:22,606 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-09-24 12:07:22,620 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.013s 
2025-09-24 12:07:22,625 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,636 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-09-24 12:07:22,658 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) starting 
2025-09-24 12:07:22,676 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) done in 0.018s 
2025-09-24 12:07:22,680 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) processed 0 records, 0 records remaining 
2025-09-24 12:07:22,688 113224 INFO kayan_whatsapp odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) completed 
