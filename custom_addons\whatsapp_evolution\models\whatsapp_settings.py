# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class WhatsAppSettings(models.Model):
    _name = 'whatsapp.settings'
    _description = 'WhatsApp Evolution API Default Settings'
    _rec_name = 'name'

    name = fields.Char(
        string='Configuration Name',
        default='WhatsApp Default Settings',
        required=True
    )

    default_server_url = fields.Char(
        string='Default Evolution API Server URL',
        help='Default server URL that will be used for new WhatsApp configurations',
        default='https://whatsapp-n8n-evolution.ufzryk.easypanel.host',
        required=True
    )

    default_global_api_key = fields.Char(
        string='Default Global API Key',
        help='Default global API key that will be used for new WhatsApp configurations',
        default='429683C4C977415CAAFCCE10F7D57E12',
        required=True
    )

    active = fields.Boolean(
        string='Active',
        default=True
    )

    # SMS Gateway Settings
    sms_enabled = fields.Boolean(
        string='Enable SMS Gateway',
        default=False,
        help='Enable SMS sending functionality using Android SMS Gateway'
    )

    sms_gateway_url = fields.Char(
        string='SMS Gateway URL',
        help='Android SMS Gateway server URL (e.g., http://*************:8080/message)',
        default='http://*************:8080/message'
    )

    sms_username = fields.Char(
        string='SMS Gateway Username',
        help='Username for SMS Gateway basic authentication'
    )

    sms_password = fields.Char(
        string='SMS Gateway Password',
        help='Password for SMS Gateway basic authentication'
    )

    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.model
    def get_default_settings(self):
        """Get the active default settings for the current company"""
        settings = self.search([
            ('active', '=', True),
            ('company_id', '=', self.env.company.id)
        ], limit=1)

        if not settings:
            # Create default settings if none exist
            settings = self.create({
                'name': 'WhatsApp Default Settings',
                'default_server_url': 'https://whatsapp-n8n-evolution.ufzryk.easypanel.host',
                'default_global_api_key': '429683C4C977415CAAFCCE10F7D57E12',
                'active': True,
                'company_id': self.env.company.id,
                # SMS Gateway default settings
                'sms_enabled': False,
                'sms_gateway_url': 'http://*************:8080/message',
                'sms_username': '',
                'sms_password': '',
            })

        return settings

    @api.model
    def get_default_server_url(self):
        """Get the default server URL from settings"""
        settings = self.get_default_settings()
        return settings.default_server_url

    @api.model
    def get_default_global_api_key(self):
        """Get the default global API key from settings"""
        settings = self.get_default_settings()
        return settings.default_global_api_key

    @api.model
    def get_sms_settings(self):
        """Get SMS gateway settings from default settings"""
        settings = self.get_default_settings()
        return {
            'enabled': settings.sms_enabled,
            'gateway_url': settings.sms_gateway_url,
            'username': settings.sms_username,
            'password': settings.sms_password,
        }

    @api.model
    def is_sms_enabled(self):
        """Check if SMS gateway is enabled"""
        settings = self.get_default_settings()
        return settings.sms_enabled and settings.sms_gateway_url and settings.sms_username and settings.sms_password

    def action_save_settings(self):
        """Save the settings and show confirmation"""
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Settings Saved'),
                'message': _('WhatsApp default settings have been saved successfully.'),
                'type': 'success',
            }
        }

    def action_test_sms(self):
        """Test SMS gateway connection"""
        self.ensure_one()

        if not self.sms_enabled:
            raise UserError(_('SMS Gateway is not enabled. Please enable it first.'))

        if not all([self.sms_gateway_url, self.sms_username, self.sms_password]):
            raise UserError(_('SMS Gateway settings are incomplete. Please fill in URL, username, and password.'))

        # Import here to avoid import issues
        import requests
        import base64

        # Test message
        test_message = "Test SMS from Odoo WhatsApp Evolution module. SMS Gateway is working!"

        # Get a test phone number from user input
        return {
            'type': 'ir.actions.act_window',
            'name': 'Test SMS',
            'res_model': 'whatsapp.sms.test.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_message': test_message,
                'default_settings_id': self.id,
            }
        }
