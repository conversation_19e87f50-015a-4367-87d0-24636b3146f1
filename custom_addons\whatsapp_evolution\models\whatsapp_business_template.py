# -*- coding: utf-8 -*-

import logging
import requests
import base64
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WhatsAppBusinessTemplate(models.Model):
    _name = 'whatsapp.business.template'
    _description = 'WhatsApp Business Message Templates'
    _order = 'sequence, template_category, template_name'
    _rec_name = 'template_name'

    # Template Definition (Read-only, Hard-coded)
    template_code = fields.Char(
        string='Template Code',
        required=True,
        readonly=True,
        help='Unique identifier for the template'
    )
    
    template_name = fields.Char(
        string='Template Name',
        required=True,
        readonly=True,
        help='Display name of the template'
    )
    
    template_category = fields.Selection([
        ('inventory', 'Inventory Management'),
        ('sales', 'Sales Reports'),
        ('alerts', 'Business Alerts'),
        ('performance', 'Performance Reports'),
        ('customer', 'Customer Communications'),
        ('financial', 'Financial Reports'),
    ], string='Category', readonly=True, required=True)
    
    template_description = fields.Text(
        string='Description',
        readonly=True,
        help='What this template does'
    )
    
    # Trigger Configuration (Read-only, Hard-coded)
    trigger_type = fields.Selection([
        ('scheduled', 'Scheduled'),
        ('event', 'Event-Based'),
        ('threshold', 'Threshold-Based'),
        ('manual', 'Manual Only'),
    ], string='Trigger Type', readonly=True, required=True)
    
    schedule_frequency = fields.Selection([
        ('realtime', 'Real-time (Individual Alerts)'),
        ('daily', 'Daily Summary'),
        ('weekly', 'Weekly Summary'),
        ('monthly', 'Monthly Summary'),
    ], string='Frequency', readonly=True)
    
    default_message = fields.Text(
        string='Default Message Template',
        readonly=True,
        help='Default message with variables'
    )
    
    available_variables = fields.Text(
        string='Available Variables',
        readonly=True,
        help='List of variables that can be used in the message'
    )
    
    # User Configuration (Editable)
    active = fields.Boolean(
        string='Active',
        default=False,
        help='Enable/disable this template'
    )
    
    custom_message = fields.Text(
        string='Custom Message',
        help='Customize the message content (use variables from default message)'
    )

    # SMS Configuration
    sms_enabled = fields.Boolean(
        string='Enable SMS',
        default=False,
        help='Enable SMS sending for this template'
    )

    sms_message = fields.Text(
        string='SMS Message',
        help='SMS-specific message content (usually shorter than WhatsApp message). Use same variables as WhatsApp message.'
    )

    send_time = fields.Float(
        string='Send Time',
        default=9.0,
        help='Time to send scheduled messages (24-hour format, e.g., 14.5 = 2:30 PM)'
    )
    
    recipients = fields.Text(
        string='Recipients',
        help='Phone numbers to send to (one per line, with country code)'
    )
    
    # Threshold Configuration (for threshold-based templates)
    threshold_value = fields.Float(
        string='Threshold Value',
        help='Trigger when value reaches this threshold'
    )
    
    threshold_operator = fields.Selection([
        ('less_than', 'Less Than'),
        ('less_equal', 'Less Than or Equal'),
        ('greater_than', 'Greater Than'),
        ('greater_equal', 'Greater Than or Equal'),
        ('equal', 'Equal To'),
    ], string='Threshold Operator', default='less_than')

    # Low Stock Alert Configuration
    max_products_per_message = fields.Integer(
        string='Max Products per Message',
        default=10,
        help='Maximum number of products to include in one message'
    )

    alert_cooldown_hours = fields.Integer(
        string='Alert Cooldown (Hours)',
        default=24,
        help='Minimum hours between alerts for the same product'
    )

    only_critical_stock = fields.Boolean(
        string='Only Critical Stock',
        default=False,
        help='Only alert when stock is completely out (0 quantity)'
    )
    
    # Configuration and Status
    config_id = fields.Many2one(
        'whatsapp.config',
        string='WhatsApp Configuration',
        help='WhatsApp configuration to use for sending messages'
    )
    
    last_sent_date = fields.Datetime(
        string='Last Sent',
        readonly=True
    )
    
    total_sent = fields.Integer(
        string='Total Messages Sent',
        readonly=True,
        default=0
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        readonly=True,
        help='Order of templates in the interface'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.model
    def get_predefined_templates(self):
        """Define all hard-coded templates here"""
        return [
            {
                'template_code': 'low_stock_alert',
                'template_name': 'Low Stock Alert',
                'template_category': 'inventory',
                'template_description': 'Alert when product stock goes below minimum level',
                'trigger_type': 'threshold',
                'schedule_frequency': 'immediate',
                'default_message': '🚨 تنبيه نفاد المخزون\n\nالمنتج: {product_name}\nالكمية المتبقية: {current_qty} {uom}\nالحد الأدنى: {min_qty} {uom}\n\n⚠️ يرجى إعادة التخزين فوراً',
                'available_variables': 'product_name, current_qty, min_qty, uom, location_name',
                'sequence': 10,
                'threshold_value': 0.0,
                'threshold_operator': 'less_equal',
            },
            # Future templates will be added here
            {
                'template_code': 'product_performance_weekly',
                'template_name': 'Weekly Product Performance',
                'template_category': 'performance',
                'template_description': 'Weekly report of best and worst performing products',
                'trigger_type': 'scheduled',
                'schedule_frequency': 'weekly',
                'default_message': '📊 تقرير أداء المنتجات الأسبوعي\n\n🔥 أفضل منتج: {best_product} ({best_qty} قطعة)\n📉 أضعف منتج: {worst_product} ({worst_qty} قطعة)\n\nالفترة: {week_start} - {week_end}',
                'available_variables': 'best_product, best_qty, worst_product, worst_qty, week_start, week_end',
                'sequence': 20,
            },
            {
                'template_code': 'below_cost_sale',
                'template_name': 'Below Cost Sale Alert',
                'template_category': 'alerts',
                'template_description': 'Alert when product is sold below cost price',
                'trigger_type': 'event',
                'schedule_frequency': 'immediate',
                'default_message': '💸 تحذير: بيع بأقل من التكلفة\n\nالمنتج: {product_name}\nسعر البيع: {sale_price}\nسعر التكلفة: {cost_price}\nالخسارة: {loss_amount}\nالعميل: {customer_name}',
                'available_variables': 'product_name, sale_price, cost_price, loss_amount, customer_name',
                'sequence': 30,
            },
            {
                'template_code': 'payment_confirmation',
                'template_name': 'Payment Confirmation',
                'template_category': 'financial',
                'template_description': 'Send payment confirmation with receipt when payment is received',
                'trigger_type': 'event',
                'schedule_frequency': 'realtime',
                'default_message': '💰 Payment Received - Thank You!\n\nDear {partner_name},\n\n✅ Payment Confirmed\n💵 Amount: {amount} {currency}\n📅 Date: {payment_date}\n💳 Method: {payment_method}\n📄 Reference: {payment_reference}\n\n💼 Account Balance:\n{balance_info}\n\nThank you for your payment!\n\n📄 Receipt will be sent separately.',
                'available_variables': 'partner_name, amount, currency, payment_date, payment_method, payment_reference, balance_info, company_name, payment_type',
                'sequence': 40,
            }
        ]

    @api.model
    def create_basic_templates(self):
        """Create basic templates - simple approach"""
        templates = [
            {
                'template_code': 'low_stock_alert',
                'template_name': 'Low Stock Alert',
                'template_category': 'inventory',
                'trigger_type': 'threshold',
                'schedule_frequency': 'immediate',
                'default_message': 'Low Stock Alert: {product_name} has {current_qty} units left',
                'custom_message': 'Low Stock Alert: {product_name} has {current_qty} units left',
                'sequence': 10,
                'active': False,
            }
        ]

        for template_data in templates:
            existing = self.search([('template_code', '=', template_data['template_code'])])
            if not existing:
                self.create(template_data)
                print(f"✅ Created template: {template_data['template_name']}")
            else:
                print(f"⚠️ Template already exists: {template_data['template_name']}")

    @api.model
    def install_predefined_templates(self):
        """Install all predefined templates"""
        templates_data = self.get_predefined_templates()
        
        for template_data in templates_data:
            existing = self.search([
                ('template_code', '=', template_data['template_code']),
                ('company_id', '=', self.env.company.id)
            ])
            
            if not existing:
                # Set custom_message to default_message initially
                template_data['custom_message'] = template_data['default_message']
                template_data['company_id'] = self.env.company.id
                
                self.create(template_data)
                _logger.info(f"Created WhatsApp template: {template_data['template_name']}")
            else:
                # Update existing template (preserve user customizations)
                update_data = {
                    'template_name': template_data['template_name'],
                    'template_description': template_data['template_description'],
                    'default_message': template_data['default_message'],
                    'available_variables': template_data['available_variables'],
                }
                existing.write(update_data)
                _logger.info(f"Updated WhatsApp template: {template_data['template_name']}")

    def get_message_content(self):
        """Get the message content to send (custom or default)"""
        self.ensure_one()
        return self.custom_message or self.default_message

    def get_config(self):
        """Get WhatsApp configuration for this template"""
        self.ensure_one()
        if self.config_id:
            return self.config_id
        
        # Use default configuration
        default_config = self.env['whatsapp.config'].get_default_config()
        if not default_config:
            raise UserError(_('No WhatsApp configuration found. Please configure WhatsApp settings first.'))
        
        return default_config

    def get_recipients_list(self):
        """Parse recipients text into list of phone numbers"""
        self.ensure_one()
        if not self.recipients:
            return []
        
        # Split by lines and clean up
        recipients = []
        for line in self.recipients.split('\n'):
            phone = line.strip()
            if phone and not phone.startswith('#'):  # Allow comments with #
                recipients.append(phone)
        
        return recipients

    def can_send_now(self):
        """Check if template can be sent now based on schedule"""
        self.ensure_one()
        
        if not self.active:
            return False
        
        if self.trigger_type in ['event', 'threshold', 'manual']:
            return True
        
        if self.trigger_type == 'scheduled':
            current_time = datetime.now()
            send_hour = int(self.send_time)
            send_minute = int((self.send_time - send_hour) * 60)
            
            if self.schedule_frequency == 'daily':
                return (current_time.hour == send_hour and 
                       current_time.minute == send_minute)
            
            elif self.schedule_frequency == 'weekly':
                return (current_time.weekday() == 6 and  # Sunday
                       current_time.hour == send_hour and
                       current_time.minute == send_minute)
            
            elif self.schedule_frequency == 'monthly':
                return (current_time.day == 1 and
                       current_time.hour == send_hour and
                       current_time.minute == send_minute)
        
        return False

    def send_template_message(self, variables=None, recipients=None):
        """Send message using this template"""
        self.ensure_one()
        
        if not self.active:
            raise UserError(_('Template "%s" is not active.') % self.template_name)
        
        config = self.get_config()
        message_content = self.get_message_content()
        
        # Replace variables in message
        if variables:
            for var_name, var_value in variables.items():
                placeholder = '{' + var_name + '}'
                message_content = message_content.replace(placeholder, str(var_value))
        
        # Get recipients
        if recipients is None:
            recipients = self.get_recipients_list()
        
        if not recipients:
            raise UserError(_('No recipients configured for template "%s".') % self.template_name)
        
        # Send WhatsApp messages
        whatsapp_sent_count = 0
        for phone_number in recipients:
            try:
                config.send_message(phone_number, message_content)
                whatsapp_sent_count += 1
            except Exception as e:
                _logger.error(f"Failed to send WhatsApp message to {phone_number}: {e}")

        # Send SMS messages if enabled
        sms_sent_count = 0
        if self.sms_enabled:
            try:
                sms_sent_count = self.send_sms_message(variables, recipients)
            except Exception as e:
                _logger.error(f"Failed to send SMS for template {self.template_name}: {e}")

        # Update statistics
        total_sent = whatsapp_sent_count + sms_sent_count
        self.write({
            'last_sent_date': fields.Datetime.now(),
            'total_sent': self.total_sent + whatsapp_sent_count,  # Only count WhatsApp for existing stats
        })

        _logger.info(f"Template {self.template_name}: WhatsApp sent: {whatsapp_sent_count}, SMS sent: {sms_sent_count}")

        return whatsapp_sent_count

    def send_sms_message(self, variables=None, recipients=None):
        """Send SMS message using Android SMS Gateway"""
        self.ensure_one()

        if not self.sms_enabled:
            _logger.info(f"SMS not enabled for template: {self.template_name}")
            return 0

        # Get SMS settings
        settings_model = self.env['whatsapp.settings']
        sms_settings = settings_model.get_sms_settings()

        if not sms_settings['enabled']:
            _logger.warning("SMS gateway is not enabled in global settings")
            return 0

        if not all([sms_settings['gateway_url'], sms_settings['username'], sms_settings['password']]):
            _logger.error("SMS gateway settings incomplete")
            return 0

        # Get SMS message content
        sms_content = self.sms_message or self.get_message_content()

        # Replace variables in SMS message
        if variables:
            for var_name, var_value in variables.items():
                placeholder = '{' + var_name + '}'
                sms_content = sms_content.replace(placeholder, str(var_value))

        # Get recipients
        if recipients is None:
            recipients = self.get_recipients_list()

        if not recipients:
            _logger.warning(f"No recipients configured for SMS template: {self.template_name}")
            return 0

        # Prepare authentication
        auth_string = f"{sms_settings['username']}:{sms_settings['password']}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Basic {auth_b64}'
        }

        # Prepare payload
        payload = {
            'textMessage': {
                'text': sms_content
            },
            'phoneNumbers': recipients
        }

        # Send SMS
        sent_count = 0
        try:
            response = requests.post(
                sms_settings['gateway_url'],
                json=payload,
                headers=headers,
                timeout=30
            )

            if response.status_code in [200, 202]:  # 200 = OK, 202 = Accepted
                sent_count = len(recipients)
                _logger.info(f"SMS sent successfully to {sent_count} recipients via template: {self.template_name}")
            else:
                _logger.error(f"SMS sending failed. Status: {response.status_code}, Response: {response.text}")

        except requests.exceptions.RequestException as e:
            _logger.error(f"SMS sending error for template {self.template_name}: {e}")
        except Exception as e:
            _logger.error(f"Unexpected error sending SMS for template {self.template_name}: {e}")

        return sent_count

    def action_send_test_message(self):
        """Send a test message with sample data"""
        self.ensure_one()
        
        # Sample variables for testing
        test_variables = {
            'product_name': 'منتج تجريبي',
            'current_qty': '5',
            'min_qty': '10',
            'uom': 'قطعة',
            'location_name': 'المخزن الرئيسي',
        }
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Send Test Message'),
            'res_model': 'whatsapp.template.test.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id,
                'default_test_variables': str(test_variables),
            }
        }

    def action_view_sent_messages(self):
        """View all sent messages for this template"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': f'Messages Sent - {self.template_name}',
            'res_model': 'whatsapp.message',
            'view_mode': 'list,form',
            'domain': [('template_id', '=', self.id)],
            'context': {'default_template_id': self.id}
        }
