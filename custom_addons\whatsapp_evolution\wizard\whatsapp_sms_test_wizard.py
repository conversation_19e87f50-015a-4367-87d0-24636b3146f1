# -*- coding: utf-8 -*-

import logging
import requests
import base64
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WhatsAppSMSTestWizard(models.TransientModel):
    _name = 'whatsapp.sms.test.wizard'
    _description = 'SMS Gateway Test Wizard'

    settings_id = fields.Many2one(
        'whatsapp.settings',
        string='Settings',
        required=True
    )

    phone_number = fields.Char(
        string='Test Phone Number',
        required=True,
        help='Phone number to send test SMS (with country code, e.g., +1234567890)'
    )

    message = fields.Text(
        string='Test Message',
        required=True,
        default='Test SMS from Odoo WhatsApp Evolution module. SMS Gateway is working!'
    )

    def action_send_test_sms(self):
        """Send test SMS"""
        self.ensure_one()
        
        settings = self.settings_id
        
        if not settings.sms_enabled:
            raise UserError(_('SMS Gateway is not enabled.'))
        
        if not all([settings.sms_gateway_url, settings.sms_username, settings.sms_password]):
            raise UserError(_('SMS Gateway settings are incomplete.'))
        
        # Prepare authentication
        auth_string = f"{settings.sms_username}:{settings.sms_password}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Basic {auth_b64}'
        }
        
        # Prepare payload
        payload = {
            'textMessage': {
                'text': self.message
            },
            'phoneNumbers': [self.phone_number]
        }
        
        try:
            response = requests.post(
                settings.sms_gateway_url,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code in [200, 202]:  # 200 = OK, 202 = Accepted
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('SMS Test Successful'),
                        'message': _('Test SMS sent successfully to %s') % self.phone_number,
                        'type': 'success',
                    }
                }
            else:
                raise UserError(_('SMS sending failed. Status: %s, Response: %s') % (response.status_code, response.text))
                
        except requests.exceptions.RequestException as e:
            raise UserError(_('SMS Gateway connection error: %s') % str(e))
        except Exception as e:
            raise UserError(_('Unexpected error: %s') % str(e))
